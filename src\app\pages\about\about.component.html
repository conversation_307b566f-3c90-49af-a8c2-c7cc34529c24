<!-- Enhanced <PERSON> Banner with <PERSON><PERSON><PERSON> Elements -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div
    class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
    style="background-image: url('https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg')">
  </div>

  <!-- Animated Gradient Background -->
  <div class="absolute inset-0 overflow-hidden animate-gradient-background">
    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>
    <!-- Animated Gradient Accent -->
    <div
      class="absolute inset-0 bg-gradient-to-tr from-accent-500/20 via-turmeric-400/10 to-transparent animate-gradient-shift">
    </div>

    <!-- <PERSON><PERSON><PERSON> Background -->
    <div class="absolute inset-0 opacity-10">
      <app-mithila-art-background [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" opacity="15">
      </app-mithila-art-background>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 right-20 w-32 h-32 rounded-full border border-white/20 opacity-30 animate-float-slow">
    </div>
    <div
      class="absolute bottom-20 left-10 w-24 h-24 rounded-full border border-white/20 opacity-20 animate-float-medium">
    </div>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'full'"
    position="top-6 left-6 right-6 bottom-6">
  </app-mithila-border>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'lotus'"
      position="relative mb-6" classes="opacity-90 animate-float-slow" size="80px">
    </app-mithila-decorative-element>

    <!-- Title with Animation -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in">
      About Mithilani Ghar
    </h1>

    <!-- Decorative Divider -->
    <div class="relative h-1 mx-auto mb-6 w-48 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-turmeric-300 via-white to-turmeric-300 rounded-full shadow-lg">
      </div>
      <div class="absolute inset-0 bg-white/30 animate-shimmer"></div>
    </div>

    <!-- Subtitle with Animation -->
    <p class="text-lg sm:text-xl md:text-2xl text-white max-w-3xl mx-auto animate-fade-in-delay">
      Preserving and promoting the rich artistic heritage of Mithila
    </p>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24"
        stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
      </svg>
    </div>
  </div>
</div>

<!-- Our Story Section -->
<app-mithila-section primaryColor="#C1440E" secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50" backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
    <div>
      <div class="relative rounded-lg overflow-hidden group">
        <!-- Decorative Border -->
        <div
          class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient">
        </div>

        <div class="relative rounded-lg overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg"
            alt="Mithilani Ghar Story" class="w-full h-auto transition-transform duration-700 group-hover:scale-105">

          <!-- Overlay Gradient -->
          <div
            class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          </div>
        </div>

        <!-- Decorative Elements -->
        <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'lotus'"
          position="absolute -top-5 -right-5" classes="opacity-80 animate-float-slow pointer-events-none" size="40px">
        </app-mithila-decorative-element>
      </div>
    </div>
    <div>
      <app-section-title title="Our Story" subtitle="How Mithilani Ghar came to be"
        alignment="left"></app-section-title>

      <div class="mt-6 space-y-4 text-gray-800">
        <p class="text-base sm:text-lg leading-relaxed">
          <strong class="text-primary-700">Mithilani Ghar</strong> was founded in 2015 by <strong
            class="text-primary-700">Sarita Devi</strong>, a master Mithila artist with a vision to preserve and promote
          the rich artistic heritage of the Mithila region. What began as a small workshop in Janakpur has grown into a
          vibrant cultural center that serves as a hub for artists, art enthusiasts, and cultural preservation.
        </p>
        <p class="text-base sm:text-lg leading-relaxed">
          Our journey started with a simple mission: to provide local artists with a platform to showcase their work and
          to ensure that the traditional art forms of Mithila continue to thrive in the modern world. Over the years, we
          have expanded our offerings to include a gallery, art store, and training center.
        </p>
        <p class="text-base sm:text-lg leading-relaxed">
          Today, Mithilani Ghar stands as a testament to the enduring beauty and cultural significance of Mithila art.
          We are proud to support a community of talented artists and to share their work with people from around the
          world.
        </p>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Our Mission Section -->
<app-mithila-section primaryColor="#F4B400" secondaryColor="#C1440E"
  backgroundGradient="from-secondary-50 via-background-light to-primary-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Mission & Values" subtitle="What drives us every day"></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
    <!-- Mission Card 1 -->
    <div
      class="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-t-4 border-primary-500 relative overflow-hidden group">
      <!-- Decorative Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-primary-50/0 via-primary-50/50 to-primary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000">
      </div>

      <div class="relative z-10">
        <div
          class="flex items-center justify-center h-16 w-16 rounded-full bg-primary-100 text-primary-600 mb-6 group-hover:bg-primary-200 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </div>
        <h3
          class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
          Preservation</h3>
        <p class="text-gray-700">
          We are dedicated to preserving the traditional techniques, motifs, and stories of Mithila art for future
          generations. Through documentation, education, and practice, we ensure this cultural heritage continues to
          thrive.
        </p>
      </div>

      <!-- Decorative Element -->
      <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'geometric'"
        position="absolute -bottom-4 -right-4"
        classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none" size="40px">
      </app-mithila-decorative-element>
    </div>

    <!-- Mission Card 2 -->
    <div
      class="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-t-4 border-secondary-500 relative overflow-hidden group">
      <!-- Decorative Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-secondary-50/0 via-secondary-50/50 to-secondary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000">
      </div>

      <div class="relative z-10">
        <div
          class="flex items-center justify-center h-16 w-16 rounded-full bg-secondary-100 text-secondary-600 mb-6 group-hover:bg-secondary-200 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
          </svg>
        </div>
        <h3
          class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-secondary-600 transition-colors duration-300">
          Promotion</h3>
        <p class="text-gray-700">
          We actively promote Mithila art on local, national, and international platforms. By organizing exhibitions,
          participating in cultural events, and leveraging digital media, we bring greater visibility to this unique art
          form.
        </p>
      </div>

      <!-- Decorative Element -->
      <app-mithila-decorative-element [primaryColor]="'#F4B400'" [secondaryColor]="'#C1440E'" [type]="'peacock'"
        position="absolute -bottom-4 -right-4"
        classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none" size="40px">
      </app-mithila-decorative-element>
    </div>

    <!-- Mission Card 3 -->
    <div
      class="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-t-4 border-accent-500 relative overflow-hidden group">
      <!-- Decorative Background -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-accent-50/0 via-accent-50/50 to-accent-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000">
      </div>

      <div class="relative z-10">
        <div
          class="flex items-center justify-center h-16 w-16 rounded-full bg-accent-100 text-accent-600 mb-6 group-hover:bg-accent-200 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-accent-600 transition-colors duration-300">
          Empowerment</h3>
        <p class="text-gray-700">
          We empower local artists, especially women, by providing them with training, resources, and economic
          opportunities. Through art, we help create sustainable livelihoods while preserving cultural identity.
        </p>
      </div>

      <!-- Decorative Element -->
      <app-mithila-decorative-element [primaryColor]="'#264653'" [secondaryColor]="'#3B945E'" [type]="'fish'"
        position="absolute -bottom-4 -right-4"
        classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none" size="40px">
      </app-mithila-decorative-element>
    </div>
  </div>
</app-mithila-section>

<!-- Our Team Section -->
<app-mithila-section primaryColor="#264653" secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-success-50" backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Meet Our Team"
    subtitle="The passionate individuals behind Mithilani Ghar"></app-section-title>

  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <div *ngFor="let member of teamMembers; let i = index"
      class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden">
        <!-- Decorative Border -->
        <div class="absolute inset-0 bg-gradient-to-br" [ngClass]="{'from-primary-300/50 via-primary-500/30 to-primary-300/50': i === 0,
                         'from-secondary-300/50 via-secondary-500/30 to-secondary-300/50': i === 1,
                         'from-accent-300/50 via-accent-500/30 to-accent-300/50': i === 2,
                         'from-success-300/50 via-success-500/30 to-success-300/50': i === 3}"></div>

        <img [src]="member.image" [alt]="member.name"
          class="relative w-full h-64 object-cover object-center transition-transform duration-500 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div
          class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        </div>

        <!-- Hover Info -->
        <div
          class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="text-white text-center p-4">
            <p class="text-lg font-medium">Click to learn more</p>
          </div>
        </div>
      </div>

      <div class="p-6 relative">
        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="i === 0 ? '#C1440E' : i === 1 ? '#F4B400' : i === 2 ? '#264653' : '#3B945E'"
          [secondaryColor]="i === 0 ? '#F4B400' : i === 1 ? '#C1440E' : i === 2 ? '#3B945E' : '#264653'"
          [type]="i === 0 ? 'lotus' : i === 1 ? 'peacock' : i === 2 ? 'fish' : 'geometric'"
          position="absolute -top-6 -right-6"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none" size="40px">
        </app-mithila-decorative-element>

        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
          {{member.name}}</h3>
        <p class="text-primary-600 mb-3 font-medium">{{member.role}}</p>
        <p class="text-gray-700">{{member.bio}}</p>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Achievements Section -->
<app-mithila-section primaryColor="#E76F51" secondaryColor="#C1440E"
  backgroundGradient="from-brick-50 via-background-light to-primary-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Achievements"
    subtitle="Milestones in our journey of preserving Mithila art"></app-section-title>

  <div class="mt-12">
    <div class="relative">
      <!-- Timeline Line -->
      <div
        class="absolute left-0 md:left-1/2 h-full w-1 bg-gradient-to-b from-primary-100 via-primary-300 to-primary-100 rounded-full transform -translate-x-1/2">
      </div>

      <!-- Timeline Items -->
      <div class="space-y-16">
        <!-- Item 1 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex items-center order-1 md:w-1/2 md:pr-12 md:text-right">
            <div class="w-full">
              <div class="mb-4">
                <span
                  class="inline-block px-4 py-1 bg-primary-100 text-primary-600 rounded-full text-sm font-medium">2015</span>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                Founding of Mithilani Ghar</h3>
              <p class="text-gray-700">Established as a small workshop in Janakpur with a mission to preserve and
                promote Mithila art.</p>
            </div>
          </div>

          <!-- Timeline Node -->
          <div
            class="absolute left-0 md:left-1/2 -translate-x-1/2 flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-primary-500 to-brick-500 text-white shadow-lg z-10">
            <span class="text-lg font-bold">1</span>
            <!-- Pulse Animation -->
            <span class="absolute w-full h-full rounded-full bg-primary-500 opacity-50 animate-ping-slow"></span>
          </div>

          <div class="order-2 md:w-1/2 md:pl-12 mt-6 md:mt-0">
            <div
              class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <!-- Decorative Border -->
              <div
                class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-brick-300 to-primary-300 rounded-lg blur-sm animate-border-gradient">
              </div>

              <div class="relative rounded-lg overflow-hidden">
                <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
                  alt="Founding of Mithilani Ghar"
                  class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

                <!-- Overlay Gradient -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-primary-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
              </div>

              <!-- Decorative Element -->
              <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#E76F51'" [type]="'lotus'"
                position="absolute -bottom-4 -right-4"
                classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none"
                size="30px">
              </app-mithila-decorative-element>
            </div>
          </div>
        </div>

        <!-- Item 2 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex items-center order-2 md:order-1 md:w-1/2 md:pr-12 md:text-right mt-6 md:mt-0">
            <div
              class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <!-- Decorative Border -->
              <div
                class="absolute -inset-1 bg-gradient-to-br from-secondary-300 via-brick-300 to-secondary-300 rounded-lg blur-sm animate-border-gradient">
              </div>

              <div class="relative rounded-lg overflow-hidden">
                <img src="https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg"
                  alt="Gallery Opening" class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

                <!-- Overlay Gradient -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-secondary-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
              </div>

              <!-- Decorative Element -->
              <app-mithila-decorative-element [primaryColor]="'#F4B400'" [secondaryColor]="'#E76F51'" [type]="'peacock'"
                position="absolute -bottom-4 -left-4"
                classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none"
                size="30px">
              </app-mithila-decorative-element>
            </div>
          </div>

          <!-- Timeline Node -->
          <div
            class="absolute left-0 md:left-1/2 -translate-x-1/2 flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-secondary-500 to-brick-500 text-white shadow-lg z-10">
            <span class="text-lg font-bold">2</span>
            <!-- Pulse Animation -->
            <span class="absolute w-full h-full rounded-full bg-secondary-500 opacity-50 animate-ping-slow"></span>
          </div>

          <div class="order-1 md:order-2 md:w-1/2 md:pl-12">
            <div class="w-full">
              <div class="mb-4">
                <span
                  class="inline-block px-4 py-1 bg-secondary-100 text-secondary-600 rounded-full text-sm font-medium">2017</span>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-secondary-600 transition-colors duration-300">
                Gallery Opening</h3>
              <p class="text-gray-700">Expanded our space to include a dedicated gallery for showcasing Mithila artwork
                to visitors.</p>
            </div>
          </div>
        </div>

        <!-- Item 3 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex items-center order-1 md:w-1/2 md:pr-12 md:text-right">
            <div class="w-full">
              <div class="mb-4">
                <span
                  class="inline-block px-4 py-1 bg-accent-100 text-accent-600 rounded-full text-sm font-medium">2019</span>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-accent-600 transition-colors duration-300">
                International Recognition</h3>
              <p class="text-gray-700">Featured in National Geographic's cultural preservation series and participated
                in exhibitions across Asia.</p>
            </div>
          </div>

          <!-- Timeline Node -->
          <div
            class="absolute left-0 md:left-1/2 -translate-x-1/2 flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-accent-500 to-brick-500 text-white shadow-lg z-10">
            <span class="text-lg font-bold">3</span>
            <!-- Pulse Animation -->
            <span class="absolute w-full h-full rounded-full bg-accent-500 opacity-50 animate-ping-slow"></span>
          </div>

          <div class="order-2 md:w-1/2 md:pl-12 mt-6 md:mt-0">
            <div
              class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <!-- Decorative Border -->
              <div
                class="absolute -inset-1 bg-gradient-to-br from-accent-300 via-brick-300 to-accent-300 rounded-lg blur-sm animate-border-gradient">
              </div>

              <div class="relative rounded-lg overflow-hidden">
                <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg"
                  alt="International Recognition"
                  class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

                <!-- Overlay Gradient -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-accent-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
              </div>

              <!-- Decorative Element -->
              <app-mithila-decorative-element [primaryColor]="'#264653'" [secondaryColor]="'#E76F51'" [type]="'fish'"
                position="absolute -bottom-4 -right-4"
                classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none"
                size="30px">
              </app-mithila-decorative-element>
            </div>
          </div>
        </div>

        <!-- Item 4 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex items-center order-2 md:order-1 md:w-1/2 md:pr-12 md:text-right mt-6 md:mt-0">
            <div
              class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <!-- Decorative Border -->
              <div
                class="absolute -inset-1 bg-gradient-to-br from-success-300 via-brick-300 to-success-300 rounded-lg blur-sm animate-border-gradient">
              </div>

              <div class="relative rounded-lg overflow-hidden">
                <img src="https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg"
                  alt="Training Program Launch"
                  class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

                <!-- Overlay Gradient -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-success-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
              </div>

              <!-- Decorative Element -->
              <app-mithila-decorative-element [primaryColor]="'#3B945E'" [secondaryColor]="'#E76F51'"
                [type]="'elephant'" position="absolute -bottom-4 -left-4"
                classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none"
                size="30px">
              </app-mithila-decorative-element>
            </div>
          </div>

          <!-- Timeline Node -->
          <div
            class="absolute left-0 md:left-1/2 -translate-x-1/2 flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-success-500 to-brick-500 text-white shadow-lg z-10">
            <span class="text-lg font-bold">4</span>
            <!-- Pulse Animation -->
            <span class="absolute w-full h-full rounded-full bg-success-500 opacity-50 animate-ping-slow"></span>
          </div>

          <div class="order-1 md:order-2 md:w-1/2 md:pl-12">
            <div class="w-full">
              <div class="mb-4">
                <span
                  class="inline-block px-4 py-1 bg-success-100 text-success-600 rounded-full text-sm font-medium">2021</span>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-success-600 transition-colors duration-300">
                Training Program Launch</h3>
              <p class="text-gray-700">Established a formal training program that has since trained over 100 new artists
                in traditional Mithila techniques.</p>
            </div>
          </div>
        </div>

        <!-- Item 5 -->
        <div class="relative flex flex-col md:flex-row items-center">
          <div class="flex items-center order-1 md:w-1/2 md:pr-12 md:text-right">
            <div class="w-full">
              <div class="mb-4">
                <span
                  class="inline-block px-4 py-1 bg-brick-100 text-brick-600 rounded-full text-sm font-medium">2023</span>
              </div>
              <h3
                class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-brick-600 transition-colors duration-300">
                Digital Expansion</h3>
              <p class="text-gray-700">Launched our online store and digital gallery, making Mithila art accessible to a
                global audience.</p>
            </div>
          </div>

          <!-- Timeline Node -->
          <div
            class="absolute left-0 md:left-1/2 -translate-x-1/2 flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-brick-500 to-primary-500 text-white shadow-lg z-10">
            <span class="text-lg font-bold">5</span>
            <!-- Pulse Animation -->
            <span class="absolute w-full h-full rounded-full bg-brick-500 opacity-50 animate-ping-slow"></span>
          </div>

          <div class="order-2 md:w-1/2 md:pl-12 mt-6 md:mt-0">
            <div
              class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <!-- Decorative Border -->
              <div
                class="absolute -inset-1 bg-gradient-to-br from-brick-300 via-primary-300 to-brick-300 rounded-lg blur-sm animate-border-gradient">
              </div>

              <div class="relative rounded-lg overflow-hidden">
                <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
                  alt="Digital Expansion" class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

                <!-- Overlay Gradient -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-brick-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
              </div>

              <!-- Decorative Element -->
              <app-mithila-decorative-element [primaryColor]="'#E76F51'" [secondaryColor]="'#C1440E'"
                [type]="'geometric'" position="absolute -bottom-4 -right-4"
                classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none"
                size="30px">
              </app-mithila-decorative-element>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Our Facilities Section -->
<app-mithila-section primaryColor="#D81B60" secondaryColor="#008C8C"
  backgroundGradient="from-magenta-50 via-background-light to-peacock-50" backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Facilities"
    subtitle="Experience the world of Mithila art at our center"></app-section-title>

  <!-- Facilities Showcase -->
  <div class="mt-12">
    <!-- Gallery Facility -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-16">
      <div class="order-2 lg:order-1">
        <div class="relative mb-4">
          <div class="inline-block relative">
            <span
              class="inline-block px-4 py-1 sm:px-6 sm:py-2 bg-gradient-to-r from-magenta-400/80 to-magenta-500/80 text-white rounded-full text-sm sm:text-base font-bold tracking-wide shadow-md">
              Art Gallery
            </span>
            <!-- Animated Glow Effect -->
            <div
              class="absolute -inset-1 bg-gradient-to-r from-magenta-300/0 via-magenta-300/40 to-magenta-300/0 rounded-full blur-md animate-pulse-slow -z-10">
            </div>
          </div>
        </div>

        <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Immersive Exhibition Space</h3>

        <div class="space-y-4 text-gray-700">
          <p>Our gallery showcases a rotating collection of traditional and contemporary Mithila artwork. The space is
            designed to highlight the intricate details and vibrant colors that make this art form so distinctive.</p>
          <p>With carefully curated lighting and spacious display areas, visitors can fully appreciate the craftsmanship
            and cultural significance of each piece. The gallery hosts both permanent collections and special
            exhibitions throughout the year.</p>

          <ul class="mt-4 space-y-2">
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-magenta-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Over 200 artworks on display</span>
            </li>
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-magenta-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Interactive displays explaining techniques and symbolism</span>
            </li>
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-magenta-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Guided tours available in multiple languages</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="order-1 lg:order-2">
        <div
          class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
          <!-- Decorative Border -->
          <div
            class="absolute -inset-1 bg-gradient-to-br from-magenta-300 via-peacock-300 to-magenta-300 rounded-lg blur-sm animate-border-gradient">
          </div>

          <div class="relative rounded-lg overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
              alt="Mithilani Ghar Art Gallery"
              class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

            <!-- Overlay Gradient -->
            <div
              class="absolute inset-0 bg-gradient-to-t from-magenta-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            </div>
          </div>

          <!-- Decorative Element -->
          <app-mithila-decorative-element [primaryColor]="'#D81B60'" [secondaryColor]="'#008C8C'" [type]="'geometric'"
            position="absolute -bottom-4 -right-4"
            classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none" size="40px">
          </app-mithila-decorative-element>
        </div>
      </div>
    </div>

    <!-- Workshop Facility -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-16">
      <div>
        <div
          class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
          <!-- Decorative Border -->
          <div
            class="absolute -inset-1 bg-gradient-to-br from-peacock-300 via-magenta-300 to-peacock-300 rounded-lg blur-sm animate-border-gradient">
          </div>

          <div class="relative rounded-lg overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg"
              alt="Mithilani Ghar Workshop Space"
              class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

            <!-- Overlay Gradient -->
            <div
              class="absolute inset-0 bg-gradient-to-t from-peacock-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            </div>
          </div>

          <!-- Decorative Element -->
          <app-mithila-decorative-element [primaryColor]="'#008C8C'" [secondaryColor]="'#D81B60'" [type]="'peacock'"
            position="absolute -bottom-4 -left-4"
            classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none" size="40px">
          </app-mithila-decorative-element>
        </div>
      </div>

      <div>
        <div class="relative mb-4">
          <div class="inline-block relative">
            <span
              class="inline-block px-4 py-1 sm:px-6 sm:py-2 bg-gradient-to-r from-peacock-500/80 to-peacock-600/80 text-white rounded-full text-sm sm:text-base font-bold tracking-wide shadow-md">
              Workshop Space
            </span>
            <!-- Animated Glow Effect -->
            <div
              class="absolute -inset-1 bg-gradient-to-r from-peacock-300/0 via-peacock-300/40 to-peacock-300/0 rounded-full blur-md animate-pulse-slow -z-10">
            </div>
          </div>
        </div>

        <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Creative Learning Environment</h3>

        <div class="space-y-4 text-gray-700">
          <p>Our workshop space is where the magic happens. Designed for both beginners and experienced artists, this
            bright, airy space provides all the tools and materials needed to learn and practice Mithila art techniques.
          </p>
          <p>Regular workshops are conducted by master artists who guide participants through the traditional methods,
            symbolism, and cultural context of Mithila painting. The space can accommodate up to 30 participants at a
            time.</p>

          <ul class="mt-4 space-y-2">
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-peacock-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Fully equipped with traditional and modern art supplies</span>
            </li>
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-peacock-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Weekly workshops for all skill levels</span>
            </li>
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-peacock-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Special programs for children and schools</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Craft Store Facility -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
      <div class="order-2 lg:order-1">
        <div class="relative mb-4">
          <div class="inline-block relative">
            <span
              class="inline-block px-4 py-1 sm:px-6 sm:py-2 bg-gradient-to-r from-primary-500/80 to-primary-600/80 text-white rounded-full text-sm sm:text-base font-bold tracking-wide shadow-md">
              Craft Store
            </span>
            <!-- Animated Glow Effect -->
            <div
              class="absolute -inset-1 bg-gradient-to-r from-primary-300/0 via-primary-300/40 to-primary-300/0 rounded-full blur-md animate-pulse-slow -z-10">
            </div>
          </div>
        </div>

        <h3 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Authentic Artisan Marketplace</h3>

        <div class="space-y-4 text-gray-700">
          <p>Our craft store offers a carefully curated selection of authentic Mithila artwork and crafts created by
            local artists. Every piece is handmade and comes with information about the artist and the cultural
            significance of the design.</p>
          <p>From traditional paintings to contemporary adaptations on textiles, ceramics, and home decor items, our
            store provides a sustainable income for artists while making this beautiful art form accessible to visitors
            from around the world.</p>

          <ul class="mt-4 space-y-2">
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Fair trade practices ensuring artists receive proper compensation</span>
            </li>
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Worldwide shipping available</span>
            </li>
            <li class="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500 mr-2 mt-0.5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Custom commissions accepted</span>
            </li>
          </ul>

          <!-- CTA Button -->
          <div class="pt-4">
            <a routerLink="/shop"
              class="group relative inline-flex items-center px-6 py-3 overflow-hidden rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-sm sm:text-base">
              <!-- Button Shine Animation -->
              <span
                class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

              <span class="relative z-10 flex items-center">
                <span>Visit Our Shop</span>
                <svg xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </span>
            </a>
          </div>
        </div>
      </div>

      <div class="order-1 lg:order-2">
        <div
          class="relative rounded-lg overflow-hidden group transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
          <!-- Decorative Border -->
          <div
            class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-turmeric-300 to-primary-300 rounded-lg blur-sm animate-border-gradient">
          </div>

          <div class="relative rounded-lg overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg"
              alt="Mithilani Ghar Craft Store"
              class="w-full h-auto transition-transform duration-700 group-hover:scale-110">

            <!-- Overlay Gradient -->
            <div
              class="absolute inset-0 bg-gradient-to-t from-primary-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            </div>
          </div>

          <!-- Decorative Element -->
          <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'fish'"
            position="absolute -bottom-4 -right-4"
            classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none" size="40px">
          </app-mithila-decorative-element>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>