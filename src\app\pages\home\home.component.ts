import { Component, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';

import { CommonModule } from '@angular/common';
import { trigger, transition, style, animate, query, stagger, state, keyframes } from '@angular/animations';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MithilaSectionComponent,
    SectionTitleComponent,
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('staggerIn', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger('200ms', [
            animate('400ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
          ]),
        ], { optional: true }),
      ]),
    ]),
    trigger('scaleIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.9)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'scale(1)' })),
      ]),
    ]),
    trigger('textTyping', [
      state('void', style({
        width: '0',
      })),
      transition('void => *', [
        animate('1.5s steps(20)', style({
          width: '100%',
        }))
      ]),
    ]),
    trigger('wordRotate', [
      transition('* => *', [
        animate('500ms', keyframes([
          style({ opacity: 0, transform: 'translateY(20px)', offset: 0 }),
          style({ opacity: 1, transform: 'translateY(0)', offset: 0.5 }),
          style({ opacity: 1, transform: 'translateY(0)', offset: 0.9 }),
          style({ opacity: 0, transform: 'translateY(-20px)', offset: 1 })
        ]))
      ])
    ]),
    trigger('fadeSlideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(30px)' }),
        animate('800ms 300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ]),
    trigger('fadeSlideInRight', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(30px)' }),
        animate('800ms 300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
      ]),
    ]),
    trigger('fadeSlideInLeft', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(-30px)' }),
        animate('800ms 300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' })),
      ]),
    ]),
  ]
})
export class HomeComponent implements OnInit {
  currentWordIndex = 0;
  rotatingWords = ['Art', 'Painting', 'Culture', 'Mithila', 'Tradition', 'Heritage'];
  currentWord = '';
  typingInterval: any;
  wordChangeInterval: any;

  ngOnInit() {
    this.startWordRotation();
  }

  startWordRotation() {
    this.wordChangeInterval = setInterval(() => {
      this.currentWordIndex = (this.currentWordIndex + 1) % this.rotatingWords.length;
      this.currentWord = this.rotatingWords[this.currentWordIndex];
    }, 3000);
  }

  ngOnDestroy() {
    if (this.typingInterval) clearInterval(this.typingInterval);
    if (this.wordChangeInterval) clearInterval(this.wordChangeInterval);
  }

  testimonials = [
    {
      id: 1,
      name: 'Rajesh Sharma',
      role: 'Art Collector',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      quote: 'The artwork I purchased from Mithilani Ghar is absolutely stunning. The attention to detail and vibrant colors truly capture the essence of Mithila art tradition.'
    },
    {
      id: 2,
      name: 'Sarah Johnson',
      role: 'Tourist',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      quote: 'Visiting Mithilani Ghar was the highlight of my trip to Janakpur. The artists were so welcoming and I learned so much about the cultural significance behind each piece.'
    },
    {
      id: 3,
      name: 'Amit Patel',
      role: 'Interior Designer',
      image: 'https://randomuser.me/api/portraits/men/67.jpg',
      quote: 'I regularly source artwork from Mithilani Ghar for my clients. The quality is consistently excellent and the pieces add a unique cultural element to any space.'
    }
  ];

  upcomingEvents = [
    {
      id: '1',
      title: 'Mithila Art Workshop',
      date: 'June 15, 2025',
      time: '10:00 AM - 2:00 PM',
      location: 'Mithilani Ghar, Janakpur',
      imageUrl: 'https://i.etsystatic.com/43638819/r/il/7b65fd/5930357750/il_794xN.5930357750_3qzn.jpg',
      description: 'Learn the basics of traditional Mithila painting techniques from master artists.'
    },
    {
      id: '2',
      title: 'Exhibition: Modern Mithila',
      date: 'July 5-15, 2025',
      time: '9:00 AM - 8:00 PM',
      location: 'Mithilani Ghar Gallery',
      imageUrl: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      description: 'A special exhibition showcasing contemporary interpretations of traditional Mithila art.'
    },
    {
      id: '3',
      title: 'Artist Talk: Preserving Heritage',
      date: 'July 20, 2025',
      time: '4:00 PM - 6:00 PM',
      location: 'Mithilani Ghar, Janakpur',
      imageUrl: 'https://i.etsystatic.com/43638819/r/il/7a099c/5978434699/il_794xN.5978434699_np8t.jpg',
      description: 'Join us for an insightful discussion on preserving cultural heritage through art.'
    }
  ];
}
