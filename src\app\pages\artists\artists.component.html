<!-- <PERSON> Banner -->
<div class="relative h-[50vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
    style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-10">
    <app-mithila-art-background [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" opacity="15">
    </app-mithila-art-background>
  </div>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'lotus'"
      position="relative mb-6" classes="opacity-90" size="80px">
    </app-mithila-decorative-element>

    <!-- Title -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
      Our Artists
    </h1>

    <!-- Subtitle -->
    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      Meet the talented artists and shop their beautiful creations
    </p>
  </div>
</div>

<!-- Artists Section -->
<app-mithila-section primaryColor="#F4B400" secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Artists"
    subtitle="Meet our talented artists who specialize in traditional Mithila art forms"></app-section-title>

  <!-- Artist Profile Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let artist of featuredArtists"
      class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">

      <!-- Artist Profile Header -->
      <div class="relative">
        <!-- Cover Image -->
        <div class="h-32 bg-gradient-to-r from-primary-600 to-secondary-600"></div>

        <!-- Profile Image -->
        <div class="absolute -bottom-12 left-6">
          <div class="w-24 h-24 rounded-full border-4 border-white shadow-lg overflow-hidden">
            <img [src]="artist.image" [alt]="artist.name" class="w-full h-full object-cover">
          </div>
        </div>
      </div>

      <!-- Artist Details -->
      <div class="pt-16 p-6">
        <div class="mb-4">
          <h3 class="text-xl font-bold text-gray-900 mb-1">{{artist.name}}</h3>
          <p class="text-primary-600 font-medium mb-2">{{artist.role}}</p>
          <p class="text-gray-600 text-sm mb-3 line-clamp-3">{{artist.bio}}</p>
        </div>

        <!-- Artist Stats -->
        <div class="grid grid-cols-2 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
          <div class="text-center">
            <p class="text-2xl font-bold text-primary-600">5+</p>
            <p class="text-xs text-gray-500">Years Experience</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-primary-600">{{artist.artworks.length}}</p>
            <p class="text-xs text-gray-500">Artworks</p>
          </div>
        </div>

        <!-- Specializations -->
        <div class="mb-4">
          <p class="text-sm font-medium text-gray-700 mb-2">Specializations:</p>
          <div class="flex flex-wrap gap-1">
            <span class="px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium">
              {{artist.role}}
            </span>
            <span class="px-2 py-1 bg-secondary-100 text-secondary-700 rounded-full text-xs font-medium">
              Traditional Art
            </span>
          </div>
        </div>

        <!-- Social Media Links -->
        <div class="flex justify-center space-x-3 mb-4">
          <a href="https://facebook.com" target="_blank"
            class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors duration-200">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
            </svg>
          </a>

          <a href="https://instagram.com" target="_blank"
            class="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full flex items-center justify-center hover:from-purple-700 hover:to-pink-700 transition-all duration-200">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.611-3.197-1.559-.748-.948-1.197-2.25-1.197-3.654 0-1.404.449-2.706 1.197-3.654.749-.948 1.9-1.559 3.197-1.559 1.297 0 2.448.611 3.197 1.559.748.948 1.197 2.25 1.197 3.654 0 1.404-.449 2.706-1.197 3.654-.749.948-1.9 1.559-3.197 1.559zm7.718-6.713H14.85v6.396h1.317v-6.396z" />
            </svg>
          </a>

          <a href="#" target="_blank"
            class="w-8 h-8 bg-gray-600 text-white rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors duration-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
          </a>
        </div>

        <!-- View Portfolio Button -->
        <button
          class="w-full py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 font-medium transform hover:scale-105">
          View Portfolio
        </button>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Spotlight Section -->
<app-mithila-section primaryColor="#264653" secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Artist Spotlight"
    subtitle="Featuring Sarita Devi, Founder & Master Artist"></app-section-title>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
    <div>
      <div class="relative rounded-lg overflow-hidden group">
        <!-- Decorative Border -->
        <div
          class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient">
        </div>

        <div class="relative rounded-lg overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
            alt="Sarita Devi" class="w-full h-auto transition-transform duration-700 group-hover:scale-105">

          <!-- Overlay Gradient -->
          <div
            class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          </div>
        </div>
      </div>
    </div>

    <div>
      <h3 class="text-2xl font-bold text-gray-900 mb-4">Sarita Devi</h3>
      <p class="text-primary-600 mb-6 font-medium">Master Artist & Founder</p>

      <div class="space-y-4 text-gray-700">
        <p>Sarita Devi began her artistic journey at the age of 12, learning traditional Mithila painting techniques
          from her grandmother. Born in a small village near Janakpur, she grew up surrounded by the rich cultural
          traditions of the Mithila region.</p>
        <p>After completing her formal education, she dedicated herself to mastering and preserving this ancient art
          form. In 2015, she established Mithilani Ghar with the vision of creating a space where artists could work,
          teach, and showcase their creations.</p>
        <p>Her paintings are characterized by intricate details, vibrant colors, and powerful storytelling that often
          explores themes of rural life, mythology, and women's experiences.</p>
      </div>

      <div class="mt-8">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Notable Achievements</h4>
        <ul class="space-y-2 text-gray-700">
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>National Award for Excellence in Traditional Arts (2018)</span>
          </li>
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>Featured artist at the South Asian Art Festival, London (2019)</span>
          </li>
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>Published in "Contemporary Folk Artists of South Asia" (2020)</span>
          </li>
        </ul>
      </div>

      <div class="mt-8">
        <a [routerLink]="['/artists', '1']"
          class="inline-block px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
          View Full Profile
        </a>
      </div>
    </div>
  </div>


  <div class="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
    <h3 class="text-2xl font-bold mb-6">Ready to Join Us?</h3>
    <p class="text-white/90 mb-8 text-lg">
      We're looking for passionate artists who specialize in traditional Mithila art forms.
      Whether you're experienced or just starting, we welcome artists who are dedicated to preserving and promoting this
      beautiful art form.
    </p>

    <div class="flex flex-wrap justify-center gap-4">
      <button (click)="openArtistApplicationForm()"
        class="bg-white text-primary-600 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
        <span class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
            </path>
          </svg>
          Apply to Join as Artist
        </span>
      </button>
      <a routerLink="/contact"
        class="bg-white/10 backdrop-blur-md text-white px-8 py-4 rounded-full font-bold hover:bg-white/20 transition-all duration-300 transform hover:scale-105 border border-white/30">
        <span class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
            </path>
          </svg>
          Contact Us First
        </span>
      </a>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Application Form Modal -->
<app-artist-application-form [isOpen]="isArtistApplicationFormOpen" (close)="closeArtistApplicationForm()"
  (submit)="onArtistApplicationSubmit($event)">
</app-artist-application-form>