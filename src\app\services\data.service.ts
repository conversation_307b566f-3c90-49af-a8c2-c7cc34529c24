import { Injectable } from '@angular/core';

// Data Interfaces
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  category: string;
  price: number;
  images: string[];
  artist: string;
  dimensions: string;
  materials: string[];
  featured: boolean;
  detailedDescription?: string;
  artistBio?: string;
  culturalSignificance?: string;
}

export interface Artist {
  id: string;
  name: string;
  role: string;
  bio: string;
  longBio?: string;
  image: string;
  coverImage?: string;
  category: string;
  featured: boolean;
  workshops?: boolean;
  specialization?: string;
  experience?: string;
  education?: string;
  awards?: string[];
  exhibitions?: string[];
  quote?: string;
  socialMedia?: {
    instagram?: string;
    twitter?: string;
    facebook?: string;
    website?: string;
  };
  artworks?: Artwork[];
}

export interface Artwork {
  id: string;
  name: string;
  slug: string;
  price: number;
  image: string;
  artist: string;
  category?: string;
  description?: string;
}

export interface LightboxImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
  artist?: string;
  category?: string;
}

export interface SocialLink {
  name: string;
  icon: string;
  url: string;
  color: string;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface ContactInfo {
  phone: string[];
  email: string;
  address: string;
  hours: string;
}

// Art Styles and Categories
export const ART_STYLES = [
  // 'Traditional Madhubani',
  // 'Contemporary Mithila',
  // 'Kohbar Art',
  // 'Bharni Style',
  // 'Katchni Style',
  // 'Tantrik Style',
  // 'Godna Style'
];

export const EXPERIENCE_LEVELS = [
  // { value: 'beginner', label: 'Beginner (0-2 years)' },
  // { value: 'intermediate', label: 'Intermediate (3-5 years)' },
  // { value: 'advanced', label: 'Advanced (6-10 years)' },
  // { value: 'expert', label: 'Expert (10+ years)' }
];

export const PRODUCT_CATEGORIES = [
  // 'All', 'Paintings', 'Clay Crafts', 'Textiles', 'Wood Crafts'
];
export const GALLERY_CATEGORIES = [
  // 'All', 'Traditional', 'Contemporary', 'Religious', 'Nature & Wildlife', 'Portraits', 'Abstract'
];

@Injectable({
  providedIn: 'root'
})
export class DataService {

  constructor() { }

  // Contact Information
  getContactInfo(): ContactInfo {
    return {
      // phone: ['+977-9814830580', '+977-9821762884'],
      phone: [],
      // email: '<EMAIL>',
      email: '',
      // address: 'Janakpur, Nepal',
      address: '',
      // hours: 'Daily 9:00 AM to 8:00 PM'
      hours: ''
    };
  }

  // Social Media Links
  getSocialLinks(): SocialLink[] {
    return [
      // {
      //   name: 'Facebook',
      //   icon: 'fab fa-facebook-f',
      //   url: 'https://facebook.com/mithilanighar',
      //   color: 'bg-blue-600 hover:bg-blue-700'
      // },
      // {
      //   name: 'Instagram',
      //   icon: 'fab fa-instagram',
      //   url: 'https://instagram.com/mithilanighar',
      //   color: 'bg-pink-600 hover:bg-pink-700'
      // },
      // {
      //   name: 'Twitter',
      //   icon: 'fab fa-twitter',
      //   url: 'https://twitter.com/mithilanighar',
      //   color: 'bg-sky-500 hover:bg-sky-600'
      // },
      // {
      //   name: 'YouTube',
      //   icon: 'fab fa-youtube',
      //   url: 'https://youtube.com/mithilanighar',
      //   color: 'bg-red-600 hover:bg-red-700'
      // }
    ];
  }

  // FAQ Items
  getFAQItems(): FAQItem[] {
    return [
      // {
      //   question: 'What are your opening hours?',
      //   answer: 'We are open daily from 9:00 AM to 8:00 PM, including holidays.'
      // },
      // {
      //   question: 'Do you offer guided tours?',
      //   answer: 'Yes, we offer guided tours of our gallery and cultural center. Please contact us in advance to schedule a tour.'
      // },
      // {
      //   question: 'How can I purchase Mithila art?',
      //   answer: 'You can purchase art directly from our gallery or through our online shop. We ship worldwide and offer secure payment options.'
      // },
      // {
      //   question: 'Do you host cultural events?',
      //   answer: 'Yes, we regularly host cultural events, workshops, and exhibitions. Check our Events page or contact us for upcoming events.'
      // }
    ];
  }

  // Product Categories
  getProductCategories(): string[] {
    return [...PRODUCT_CATEGORIES];
  }

  // Gallery Categories
  getGalleryCategories(): string[] {
    return [...GALLERY_CATEGORIES];
  }

  // Art Styles
  getArtStyles(): string[] {
    return [...ART_STYLES];
  }

  // Experience Levels
  getExperienceLevels(): { value: string; label: string }[] {
    return [...EXPERIENCE_LEVELS];
  }

  // Products Data
  getProducts(): Product[] {
    return [
      // {
      //   id: '1',
      //   name: 'Traditional Mithila Painting - Madhubani Art',
      //   slug: 'traditional-mithila-painting-madhubani-art',
      //   description: 'Authentic hand-painted Mithila artwork featuring traditional motifs and vibrant colors on handmade paper.',
      //   category: 'Paintings',
      //   price: 2500,
      //   images: [
      //     'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      //     'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
      //   ],
      //   artist: 'Sita Devi',
      //   dimensions: '16" x 12"',
      //   materials: ['Handmade Paper', 'Natural Pigments', 'Bamboo Brush'],
      //   featured: true,
      //   detailedDescription: 'This exquisite Madhubani painting represents the finest tradition of Mithila art. Hand-painted using natural pigments and traditional techniques passed down through generations.',
      //   artistBio: 'Sita Devi is a renowned Madhubani artist with over 20 years of experience in traditional Mithila painting.',
      //   culturalSignificance: 'Madhubani paintings are traditionally created by women in the Mithila region and often depict Hindu deities, nature, and social events.'
      // },
      // {
      //   id: '2',
      //   name: 'Handcrafted Clay Elephant',
      //   slug: 'handcrafted-clay-elephant',
      //   description: 'Beautiful clay elephant sculpture with intricate Mithila patterns, perfect for home decoration.',
      //   category: 'Clay Crafts',
      //   price: 1200,
      //   images: [
      //     'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg'
      //   ],
      //   artist: 'Ramesh Kumar',
      //   dimensions: '8" x 6" x 4"',
      //   materials: ['Natural Clay', 'Natural Pigments', 'Protective Coating'],
      //   featured: false
      // },
      // {
      //   id: '3',
      //   name: 'Mithila Art Saree',
      //   slug: 'mithila-art-saree',
      //   description: 'Beautiful cotton saree with hand-painted Mithila designs, blending tradition with contemporary fashion.',
      //   category: 'Textiles',
      //   price: 4500,
      //   images: [
      //     'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
      //   ],
      //   artist: 'Kamala Devi',
      //   dimensions: '6 yards',
      //   materials: ['Pure Cotton', 'Natural Dyes', 'Hand Painting'],
      //   featured: true
      // },
      // {
      //   id: '4',
      //   name: 'Wooden Jewelry Box with Mithila Art',
      //   slug: 'wooden-jewelry-box-mithila-art',
      //   description: 'Elegant wooden jewelry box decorated with traditional Mithila patterns and motifs.',
      //   category: 'Wood Crafts',
      //   price: 2200,
      //   images: [
      //     'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
      //   ],
      //   artist: 'Mohan Lal',
      //   dimensions: '10" x 8" x 4"',
      //   materials: ['Teak Wood', 'Natural Varnish', 'Hand Painting'],
      //   featured: false
      // },
      // {
      //   id: '5',
      //   name: 'Traditional Clay Water Pot',
      //   slug: 'traditional-clay-water-pot',
      //   description: 'Authentic clay water pot with beautiful Mithila designs, functional and decorative.',
      //   category: 'Clay Crafts',
      //   price: 800,
      //   images: [
      //     'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg'
      //   ],
      //   artist: 'Sunita Devi',
      //   dimensions: '12" x 10"',
      //   materials: ['Natural Clay', 'Traditional Glazing', 'Hand Painting'],
      //   featured: false
      // },
      // {
      //   id: '6',
      //   name: 'Handwoven Mithila Table Runner',
      //   slug: 'handwoven-mithila-table-runner',
      //   description: 'Elegant table runner with woven Mithila patterns, perfect for dining room decoration.',
      //   category: 'Textiles',
      //   price: 1800,
      //   images: [
      //     'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
      //   ],
      //   artist: 'Sunita Kumari',
      //   dimensions: '72" x 14"',
      //   materials: ['Cotton Thread', 'Natural Dyes', 'Hand Weaving'],
      //   featured: false
      // }
    ];
  }

  // Get product by slug
  getProductBySlug(slug: string): Product | undefined {
    return this.getProducts().find(product => product.slug === slug);
  }

  // Get featured products
  getFeaturedProducts(): Product[] {
    return this.getProducts().filter(product => product.featured);
  }

  // Get products by category
  getProductsByCategory(category: string): Product[] {
    if (category === 'All') {
      return this.getProducts();
    }
    return this.getProducts().filter(product => product.category === category);
  }

  // Artists Data
  getArtists(): Artist[] {
    return [
      // {
      //   id: '1',
      //   name: 'Sarita Devi',
      //   role: 'Master Artist & Founder',
      //   bio: 'With over 25 years of experience in traditional Mithila art, Sarita founded Mithilani Ghar to preserve and promote this unique cultural heritage.',
      //   longBio: 'Sarita Devi began her artistic journey at the age of 12, learning traditional Mithila painting techniques from her grandmother. Born in a small village near Janakpur, she grew up surrounded by the rich cultural traditions of the Mithila region. After completing her formal education, she dedicated herself to mastering and preserving this ancient art form. In 2015, she established Mithilani Ghar with the vision of creating a space where artists could work, teach, and showcase their creations. Her paintings are characterized by intricate details, vibrant colors, and powerful storytelling that often explores themes of rural life, mythology, and women\'s experiences.',
      //   image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      //   category: 'Traditional',
      //   featured: true,
      //   workshops: true,
      //   specialization: 'Traditional Madhubani Painting',
      //   experience: '25+ years',
      //   education: 'Self-taught, Traditional Family Training',
      //   awards: [
      //     'National Award for Excellence in Traditional Arts (2018)',
      //     'Featured artist at the South Asian Art Festival, London (2019)',
      //     'Published in "Contemporary Folk Artists of South Asia" (2020)'
      //   ],
      //   exhibitions: [
      //     'South Asian Art Festival, London (2019)',
      //     'Traditional Arts Exhibition, Kathmandu (2018)',
      //     'Cultural Heritage Showcase, New Delhi (2017)'
      //   ],
      //   quote: 'Art is not just about creating beauty; it\'s about preserving our cultural identity and passing it on to future generations.',
      //   socialMedia: {
      //     instagram: 'sarita_mithila_art',
      //     facebook: 'SaritaDeviArt',
      //     website: 'www.saritadeviart.com'
      //   },
      //   artworks: [
      //     {
      //       id: 'art-1',
      //       name: 'Traditional Fish Motif',
      //       slug: 'traditional-fish-motif',
      //       price: 15000,
      //       image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      //       artist: 'Sarita Devi',
      //       category: 'Traditional',
      //       description: 'A beautiful traditional fish motif representing prosperity and fertility in Mithila culture.'
      //     },
      //     {
      //       id: 'art-2',
      //       name: 'Lotus Garden',
      //       slug: 'lotus-garden',
      //       price: 18000,
      //       image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      //       artist: 'Sarita Devi',
      //       category: 'Traditional',
      //       description: 'Intricate lotus patterns symbolizing purity and spiritual awakening.'
      //     }
      //   ]
      // },
      // {
      //   id: '2',
      //   name: 'Kamala Devi',
      //   role: 'Contemporary Artist',
      //   bio: 'Kamala specializes in contemporary interpretations of traditional Mithila art, bringing modern perspectives to ancient techniques.',
      //   image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      //   category: 'Contemporary',
      //   featured: true,
      //   workshops: false,
      //   specialization: 'Contemporary Mithila Art',
      //   experience: '15 years',
      //   education: 'BFA from Lalit Kala Academy',
      //   awards: [
      //     'Young Artist Award, Nepal Art Council (2019)',
      //     'Innovation in Traditional Art Prize (2021)'
      //   ],
      //   exhibitions: [
      //     'Solo Exhibition: "Tradition Reimagined", Gallery Nepal, Kathmandu (2023)',
      //     'Asian Contemporary Art Fair, Tokyo (2022)',
      //     'Emerging Artists Showcase, Mumbai (2021)'
      //   ],
      //   quote: 'I believe in honoring our traditions while embracing the possibilities of contemporary expression.',
      //   socialMedia: {
      //     instagram: 'kamala_contemporary_art',
      //     twitter: 'KamalaDeviArt'
      //   }
      // },
      // {
      //   id: '3',
      //   name: 'Sunil Yadav',
      //   role: 'Environmental Artist',
      //   bio: 'Sunil combines traditional Mithila techniques with environmental themes, creating awareness about nature conservation through art.',
      //   image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      //   category: 'Nature & Wildlife',
      //   featured: false,
      //   workshops: true,
      //   specialization: 'Environmental Mithila Art',
      //   experience: '12 years',
      //   education: 'Environmental Studies & Traditional Art Training',
      //   awards: [
      //     'Environmental Art Prize, WWF Nepal (2022)',
      //     'Conservation Through Art Grant (2021)',
      //     'Emerging Environmental Artist Award (2020)'
      //   ],
      //   exhibitions: [
      //     'Exhibition "Natural Heritage in Folk Art", National Museum (2023)',
      //     'Environmental Art Showcase, Kathmandu (2022)',
      //     'Biodiversity Through Art, Chitwan (2021)'
      //   ],
      //   quote: 'My art is a bridge between our cultural traditions and our natural environment. By depicting local wildlife and plants through Mithila art, I hope to remind people that our cultural heritage and natural heritage are deeply interconnected.',
      //   socialMedia: {
      //     instagram: 'sunil_nature_art',
      //     twitter: 'SunilYadavArt',
      //     facebook: 'SunilYadavNatureArt'
      //   }
      // }
    ];
  }

  // Get artist by ID
  getArtistById(id: string): Artist | undefined {
    return this.getArtists().find(artist => artist.id === id);
  }

  // Get featured artists
  getFeaturedArtists(): Artist[] {
    return this.getArtists().filter(artist => artist.featured);
  }

  // Gallery Images Data
  getGalleryImages(): LightboxImage[] {
    return [
      // {
      //   src: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      //   alt: 'Traditional Mithila Art',
      //   title: 'Madhubani Fish',
      //   artist: 'Sarita Devi',
      //   category: 'Traditional',
      //   description: 'A beautiful traditional Madhubani painting featuring fish motifs, symbolizing prosperity and good fortune.'
      // },
      // {
      //   src: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      //   alt: 'Peacock Mithila Art',
      //   title: 'Royal Peacock',
      //   artist: 'Kamala Devi',
      //   category: 'Traditional',
      //   description: 'Intricate peacock design representing beauty and grace in traditional Mithila art style.'
      // },
      // {
      //   src: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      //   alt: 'Lotus Mithila Art',
      //   title: 'Sacred Lotus',
      //   artist: 'Sunita Jha',
      //   category: 'Religious',
      //   description: 'Sacred lotus motifs with intricate geometric patterns, representing purity and spiritual awakening.'
      // },
      // {
      //   src: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      //   alt: 'Tree of Life',
      //   title: 'Tree of Life',
      //   artist: 'Meera Sharma',
      //   category: 'Nature & Wildlife',
      //   description: 'A magnificent tree of life painting showcasing the connection between earth and sky.'
      // },
      // {
      //   src: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
      //   alt: 'Contemporary Mithila',
      //   title: 'Modern Fusion',
      //   artist: 'Priya Kumari',
      //   category: 'Contemporary',
      //   description: 'A contemporary interpretation of traditional Mithila art with modern color palettes.'
      // },
      // {
      //   src: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
      //   alt: 'Portrait Art',
      //   title: 'Village Woman',
      //   artist: 'Rekha Devi',
      //   category: 'Portraits',
      //   description: 'A beautiful portrait of a village woman adorned with traditional jewelry and clothing.'
      // },
      // {
      //   src: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
      //   alt: 'Abstract Mithila',
      //   title: 'Geometric Dreams',
      //   artist: 'Anita Singh',
      //   category: 'Abstract',
      //   description: 'Abstract interpretation of traditional Mithila patterns with bold geometric designs.'
      // },
      // {
      //   src: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
      //   alt: 'Religious Art',
      //   title: 'Ganesha Blessing',
      //   artist: 'Sita Devi',
      //   category: 'Religious',
      //   description: 'Lord Ganesha depicted in traditional Mithila style, bringing blessings and removing obstacles.'
      // }
    ];
  }

  // Get gallery images by category
  getGalleryImagesByCategory(category: string): LightboxImage[] {
    if (category === 'All') {
      return this.getGalleryImages();
    }
    return this.getGalleryImages().filter(image => image.category === category);
  }
}
